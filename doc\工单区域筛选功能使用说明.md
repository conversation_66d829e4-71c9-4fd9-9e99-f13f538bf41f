# 工单区域筛选功能使用说明

## 功能介绍

工单页面新增了按区域筛选的功能，用户可以通过选择特定区域来筛选显示该区域的工单，同时导出功能也会按照选择的区域进行筛选。

## 使用方法

### 1. 页面筛选

1. 打开工单管理页面
2. 在搜索条件区域找到"区域"筛选项
3. 点击区域下拉框，选择需要筛选的区域
4. 点击"搜索"按钮，页面将显示该区域的工单
5. 如需清除区域筛选，可以点击区域下拉框的清除按钮，或点击"重置"按钮

### 2. 导出功能

1. 设置好筛选条件（包括区域筛选）
2. 点击"导出"按钮
3. 系统将按照当前的筛选条件（包括区域）导出工单数据
4. 导出的Excel文件将只包含符合筛选条件的工单

### 3. 组合筛选

区域筛选可以与其他筛选条件组合使用：

- **区域 + 保险类型**：筛选特定区域的特定保险类型工单
- **区域 + 医院**：筛选特定区域的特定医院工单
- **区域 + 保险公司**：筛选特定区域的特定保险公司工单
- **区域 + 时间范围**：筛选特定区域在特定时间范围内的工单
- **区域 + 工单状态**：筛选特定区域的特定状态工单

## 技术实现

### 前端实现
- 使用现有的 `AreaSelect` 组件提供区域选择功能
- 区域ID作为查询参数传递给后端API
- 支持清空选择和重置功能

### 后端实现
- 在查询参数VO中添加 `areaId` 字段
- 在数据库查询中添加 `area_id` 筛选条件
- 同时支持分页查询和导出查询

### 数据库关系
- 工单表 `insurance_work_order2` 包含 `area_id` 字段
- 通过 `area_id` 关联到区域表 `insurance_area`
- 筛选时直接根据 `area_id` 进行过滤

## 注意事项

1. **权限要求**：使用区域筛选功能需要有工单查询权限
2. **数据完整性**：只有包含区域信息的工单才能被区域筛选功能筛选到
3. **性能考虑**：区域筛选会在数据库层面进行，不会影响查询性能
4. **兼容性**：区域筛选是可选功能，不选择区域时显示所有有权限查看的工单

## 常见问题

**Q: 为什么有些工单在选择区域后看不到？**
A: 可能是因为这些工单没有关联到选择的区域，或者工单数据中缺少区域信息。

**Q: 区域筛选会影响导出功能吗？**
A: 是的，导出功能会按照当前页面的筛选条件进行，包括区域筛选。

**Q: 可以同时选择多个区域吗？**
A: 当前版本只支持选择单个区域，如需多区域筛选，可以分别导出后合并数据。

**Q: 区域筛选对性能有影响吗？**
A: 区域筛选在数据库层面进行，通过索引优化，对性能影响很小。
