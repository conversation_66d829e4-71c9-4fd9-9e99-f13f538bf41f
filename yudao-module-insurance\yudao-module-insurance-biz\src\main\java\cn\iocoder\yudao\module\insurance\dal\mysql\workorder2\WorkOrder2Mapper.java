package cn.iocoder.yudao.module.insurance.dal.mysql.workorder2;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.date.DateUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.insurance.controller.admin.statistics.vo.*;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrder2DO;
import cn.iocoder.yudao.module.insurance.dal.dataobject.workorder2.WorkOrderPdfDo;
import cn.iocoder.yudao.module.insurance.enums.workorder.InsuranceTypeEnum;
import cn.iocoder.yudao.module.insurance.enums.workorder.WorkOrderStatusEnum;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 工单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WorkOrder2Mapper extends BaseMapperX<WorkOrder2DO> {

    default PageResult<WorkOrder2DO> selectPage(WorkOrder2PageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WorkOrder2DO>()
                .eqIfPresent(WorkOrder2DO::getHospitalCode, reqVO.getHospitalCode())
                .likeIfPresent(WorkOrder2DO::getHospitalName, reqVO.getHospitalName())
                .likeIfPresent(WorkOrder2DO::getName, reqVO.getName())
                .eqIfPresent(WorkOrder2DO::getIdCardNumber, reqVO.getIdCardNumber())
                .eqIfPresent(WorkOrder2DO::getAddress, reqVO.getAddress())
                .eqIfPresent(WorkOrder2DO::getMobilePhoneNumber, reqVO.getMobilePhoneNumber())
                .eqIfPresent(WorkOrder2DO::getSocialMedicareCardNumber, reqVO.getSocialMedicareCardNumber())
                .eqIfPresent(WorkOrder2DO::getStatus, reqVO.getStatus())
                .inIfPresent(WorkOrder2DO::getStatus, reqVO.getStatusList())
                .eqIfPresent(WorkOrder2DO::getTreatmentSerialNumberType, reqVO.getTreatmentSerialNumberType())
                .eqIfPresent(WorkOrder2DO::getCompleteStatus, reqVO.getCompleteStatus())
                .eqIfPresent(WorkOrder2DO::getAreaId, reqVO.getAreaId())
                .betweenIfPresent(WorkOrder2DO::getCreateTime, reqVO.getBeginCreateTime(), reqVO.getEndCreateTime())
                .orderByDesc(WorkOrder2DO::getId));
    }

    default List<WorkOrder2DO> selectZyWorkOrderList(WorkOrder2ExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<WorkOrder2DO>()
                .eqIfPresent(WorkOrder2DO::getHospitalCode, reqVO.getHospitalCode())
                .likeIfPresent(WorkOrder2DO::getHospitalName, reqVO.getHospitalName())
                .likeIfPresent(WorkOrder2DO::getName, reqVO.getName())
                .eqIfPresent(WorkOrder2DO::getIdCardNumber, reqVO.getIdCardNumber())
                .eqIfPresent(WorkOrder2DO::getAddress, reqVO.getAddress())
                .eqIfPresent(WorkOrder2DO::getMobilePhoneNumber, reqVO.getMobilePhoneNumber())
                .eqIfPresent(WorkOrder2DO::getSocialMedicareCardNumber, reqVO.getSocialMedicareCardNumber())
                .eqIfPresent(WorkOrder2DO::getStatus, reqVO.getStatus())
                .eqIfPresent(WorkOrder2DO::getAreaId, reqVO.getAreaId())
                .betweenIfPresent(WorkOrder2DO::getCreateTime, reqVO.getBeginCreateTime(), reqVO.getEndCreateTime())
                .orderByDesc(WorkOrder2DO::getId));
    }

    List<WorkOrder2ExcelVO> selectList2(@Param("reqVO") WorkOrder2ExportReqVO reqVO);

    IPage<WorkOrder2DO> selectPage2(IPage<WorkOrder2DO> page, @Param("reqVO") WorkOrder2OpenApiPageReqVO reqVO);

    IPage<WorkOrder2DO> selectPage2WithStatusList(IPage<WorkOrder2DO> page, @Param("reqVO") WorkOrder2OpenApiPageReqVO reqVO, @Param("statusList") List<Integer> statusList);

    BigDecimal countCompensatingMoney();

    List<InsuranceCompensatingMoney> countCompensatingMoneyGroupByCompany();

    List<CompensatingMonthPersonNumber> countCompensatingPersonNumberGroupByMonth(@Param("year") Integer year);

    List<CompensatingAreaPersonNumber> countCompensatingPersonNumberGroupByArea(@Param("year") Integer year);

    @InterceptorIgnore(tenantLine = "true")
    IPage<WorkOrder2RespVO> selectWorkOrderPage(IPage<WorkOrder2RespVO> mpPage,  @Param("reqVO") WorkOrder2PageReqVO pageVO);

    IPage<CompensatingPersonAndCompany> selectCompensatingPersonAndCompanyPage(IPage<CompensatingPersonAndCompany> page, @Param("reqVO") WorkOrder2PageReqVO pageVO);

    BigDecimal countSuggestCompensatingMoney();

    List<WorkOrderStatisticsByArea> countWorkOrderGroupByArea();

    WorkOrderStatisticsByAge countWorkOrderGroupByAge(@Param("beginAge") Integer beginAge, @Param("endAge") Integer endAge);

    WorkOrderStatisticsByDiagnosisCodeAndAge countWorkOrderByDiagnosisCodeAndAge(@Param("diagnosisCode") String diagnosisCode, @Param("beginAge") Integer beginAge, @Param("endAge") Integer endAge);

    List<WorkOrderStatisticsByDiagnosisCodeAndArea> countWorkOrderByDiagnosisCodeGroupByArea(@Param("diagnosisCode") String diagnosisCode);

    List<WorkOrderStatisticsByDiagnosisCodeAndMonth> countWorkOrderByDiagnosisCodeGroupByMonth(@Param("diagnosisCode") String diagnosisCode, @Param("year") Integer year);

    List<WorkOrderStatisticsByDiagnosisCodeAndSex> countWorkOrderByDiagnosisCodeGroupBySex(@Param("diagnosisCode") String diagnosisCode);

    List<TopDiagnosis> countTopDiagnosis(@Param("top") Integer top);

    default Long countWorkOrder(List<InsuranceTypeEnum> types, Integer status) {
        return selectCount(new LambdaQueryWrapperX<WorkOrder2DO>()
                .in(WorkOrder2DO::getType, types.stream().map(InsuranceTypeEnum::getCode).collect(Collectors.toList()))
                .eq(WorkOrder2DO::getStatus, status));
    }

    List<String> selectDistinctHospitalNameList();

    default List<WorkOrder2DO> selectZyWorkOrderList(String idCard, Date inHospitalTime, Date outHospitalTime) {
        LocalDate inHospitalDate = DateUtils.dateToLocalDate(inHospitalTime);
        LocalDate outHospitalDate = DateUtils.dateToLocalDate(outHospitalTime);

        return selectList(new LambdaQueryWrapperX<WorkOrder2DO>()
                .eq(WorkOrder2DO::getIdCardNumber, idCard)
                .eq(WorkOrder2DO::getType, 1)
                .between(WorkOrder2DO::getInHospitalDatetime, DateUtils.localDateTimeToDate(inHospitalDate.atStartOfDay()), DateUtils.localDateTimeToDate(LocalDateTime.of(inHospitalDate, LocalTime.MAX)))
                .between(WorkOrder2DO::getLeaveHospitalDatetime, DateUtils.localDateTimeToDate(outHospitalDate.atStartOfDay()), DateUtils.localDateTimeToDate(LocalDateTime.of(outHospitalDate, LocalTime.MAX))));
    }

    default List<WorkOrder2DO> selectMzWorkOrderList(String idCard, Date treatmentTime) {
        LocalDate treatmentDate = DateUtils.dateToLocalDate(treatmentTime);
        return selectList(new LambdaQueryWrapperX<WorkOrder2DO>()
                .eq(WorkOrder2DO::getIdCardNumber, idCard)
                .eq(WorkOrder2DO::getType, 1)
                .between(WorkOrder2DO::getTreatmentDatetime, DateUtils.localDateTimeToDate(treatmentDate.atStartOfDay()), DateUtils.localDateTimeToDate(LocalDateTime.of(treatmentDate, LocalTime.MAX))));
    }

    default List<WorkOrder2DO> getWorkOrder2ListWithoutElecTicket() {
        return selectList(new LambdaQueryWrapperX<WorkOrder2DO>()
                .eq(WorkOrder2DO::getElectronicBillIds, "")
                .ne(WorkOrder2DO::getStatus, WorkOrderStatusEnum.FINISHED.getStatus()));
    }

    List<WorkOrderStatRespVo> getWorkOrderStat(@Param(value = "reqVo") WorkOrderStatReqVo reqVo);

    /**
     * 获取医院数据量统计
     * 
     * @return 医院数据量统计列表
     */
    List<HospitalDataStatisticsVO> getHospitalDataStatistics();

    /**
     * 获取医院详细统计
     * 
     * @param hospitalName 医院名称
     * @return 医院详细统计
     */
    HospitalDetailStatisticsVO getHospitalDetailStatistics(@Param("hospitalName") String hospitalName);

    /**
     * 获取保险公司接单量分析
     *
     * @return 保险公司接单量分析列表
     */
    List<InsuranceCompanyAnalysisVO> getInsuranceCompanyAnalysis();

    /**
     * 获取保险公司工单明细统计
     *
     * @param companyId 保险公司ID
     * @return 保险公司工单明细统计
     */
    InsuranceCompanyDetailVO getInsuranceCompanyDetail(@Param("companyId") Long companyId);

    /**
     * 获取工单PDF总数
     * 
     * @return PDF总数
     */
    Long countWorkOrderPdf();

    /**
     * 获取已签章工单PDF数量
     * 
     * @return 已签章PDF数量
     */
    Long countSignedWorkOrderPdf();

    /**
     * 根据时间段统计工单数量
     *
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 工单数量
     */
    Long countWorkOrderByTimeRange(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    default boolean existsWorkOrder(String idCardNumber, String hospitalCode, String treatmentSerialNumber, Integer treatmentSerialNumberType) {
        LambdaQueryWrapperX<WorkOrder2DO> queryWrapper = new LambdaQueryWrapperX<WorkOrder2DO>()
                .eq(WorkOrder2DO::getIdCardNumber, idCardNumber)
                .eq(WorkOrder2DO::getHospitalCode, hospitalCode)
                .eq(WorkOrder2DO::getTreatmentSerialNumber, treatmentSerialNumber)
                .eq(WorkOrder2DO::getTreatmentSerialNumberType, treatmentSerialNumberType);
        return selectCount(queryWrapper) > 0;
    }

    default WorkOrder2DO selectWorkOrder2BySupplementaryFileRecordId(Long supplementaryFileRecordId) {
        return selectOne(new LambdaQueryWrapperX<WorkOrder2DO>()
                .eq(WorkOrder2DO::getSupplementaryFileRecordId, supplementaryFileRecordId));
    }

    // Authorization-aware count methods
    Long countWithAuth();

    Long countWaitAcceptOrderWithAuth();

    Long countWaitHandleWithAuth();

    Long countWaitVisitingWithAuth();

    Long countFinishedWithAuth();

    Long countRejectedWithAuth();

    Long countDelayWithAuth();

    Long countSumAcceptOrderWithAuth();

    Long countNoDataOrderWithAuth();

    Long countUnsureOrderWithAuth();

    Long countCompleteOrderWithAuth();

    Long countWorkOrderWithAuth(@Param("types") List<Integer> types, @Param("status") Integer status);

    List<String> selectDistinctHospitalNameListWithAuth();

    default PageResult<WorkOrder2DO> selectSignedPdfWorkOrderPage(WorkOrder2PageReqVO reqVO) {
        return selectJoinPage(reqVO, WorkOrder2DO.class, new MPJLambdaWrapper<WorkOrder2DO>()
                .selectAll(WorkOrder2DO.class)
                .leftJoin(WorkOrderPdfDo.class, WorkOrderPdfDo::getOrderId, WorkOrder2DO::getId)
                .isNotNull(WorkOrderPdfDo::getSignedPdf)
                .orderByDesc(WorkOrder2DO::getId));
    }

    /**
     * 查询符合批量拒绝条件的工单（只查询必要字段）
     *
     * @param cutoffDate 截止日期
     * @return 工单ID和状态列表
     */
    List<WorkOrder2DO> selectWorkOrdersForBatchReject(@Param("cutoffDate") Date cutoffDate);

    /**
     * 查询符合批量恢复条件的工单（只查询必要字段）
     *
     * @param batchOperationId 批次操作ID
     * @return 工单ID、状态和原始状态列表
     */
    List<WorkOrder2DO> selectWorkOrdersForBatchRecover(@Param("batchOperationId") String batchOperationId);

    /**
     * 批量更新工单状态为拒绝状态
     *
     * @param workOrderIds 工单ID列表
     * @param batchId 批次ID
     * @return 更新的记录数
     */
    int batchUpdateWorkOrdersToReject(@Param("workOrderIds") List<Long> workOrderIds, @Param("batchId") String batchId);

    /**
     * 批量恢复工单状态
     *
     * @param workOrderIds 工单ID列表
     * @return 更新的记录数
     */
    int batchRecoverWorkOrders(@Param("workOrderIds") List<Long> workOrderIds);
}
