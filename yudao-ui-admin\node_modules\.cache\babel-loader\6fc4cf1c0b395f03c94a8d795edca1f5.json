{"remainingRequest": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\wandaWorkOrder\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\wandaWorkOrder\\index.vue", "mtime": 1754445084304}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1667694382043}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1667694382624}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\babel.config.js", "mtime": 1691889630125}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1667694382043}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1667694382624}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AA6XA;;AAqBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;EACAA,kBADA;EAEAC,gBAFA;EAGAC,sBAHA;EAIAC;AAJA;eAOA;EACAC,sBADA;EAEAC;IACAC;EADA,CAFA;EAKAC,IALA,kBAKA;IACA;MACA;MACAC,aAFA;MAGA;MACAC,oBAJA;MAKA;MACAC,gBANA;MAOA;MACAC,QARA;MASA;MACAC,QAVA;MAWA;MACAC,SAZA;MAaA;MACAC,WAdA;MAeAC,8BAfA;MAgBAC,kCAhBA;MAiBAC,uBAjBA;MAkBA;MACAC;QACAC,SADA;QAEAC,YAFA;QAGAC,kBAHA;QAIAC,kBAJA;QAKAC,+BALA;QAMAC,2BANA;QAOApB,UAPA;QAQAqB,kBARA;QASAC,uBATA;QAUAC,oBAVA;QAWAC,gBAXA;QAYAC,uBAZA;QAaAC,eAbA;QAcAC,uBAdA;QAeAC,YAfA;QAgBAC;MAhBA,CAnBA;MAqCA;MACAC,QAtCA;MAuCA;MACAC,SAxCA;MA0CA;MACAC,wBA3CA;MA4CAC;QACAC;MADA,CA5CA;MA+CAC,kBA/CA;MAgDAC,eAhDA;MAiDAC,cAjDA;MAkDAC,gBAlDA;MAmDAC,4BAnDA;MAoDAC;QACAC;UACAC,YADA;UAEAC,OAFA,mBAEAC,MAFA,EAEA;YACA;YACA;YACAC;YACAD;UACA;QAPA,GAQA;UACAF,aADA;UAEAC,OAFA,mBAEAC,MAFA,EAEA;YACA;YACA;YACAC;YACAD;UACA;QAPA,CARA;MADA,CApDA;MAuEAE,gBAvEA;MAwEAC,oBAxEA;MAyEAC,kBAzEA;MA0EAC,iBA1EA;MA2EAC,oBA3EA;MA4EAC,mBA5EA;MA6EAC;QACAP,SADA;QAEAQ;MAFA,CA7EA;MAiFAC,yBAjFA;MAkFAC,aAlFA;MAmFAC,aAnFA;MAoFAC,mCApFA;MAqFAC,6BArFA;MAsFAC,mBAtFA;MAuFAC,iBAvFA;MAwFA;MACAC,kCAzFA;MA0FAC,gBA1FA;MA2FAC,cA3FA;MA4FAC,mBA5FA;MA6FAC,iBA7FA;MA8FAC;IA9FA;EAgGA,CAtGA;EAuGAC;IACAC,iBADA,+BACA;MACA,8BACA,mFADA,KAEA,qCAFA;IAGA;EALA,CAvGA;EA8GAC,OA9GA,qBA8GA;IACA;IACA;EACA,CAjHA;EAkHAC;IACA;IACAC,OAFA,qBAEA;MAAA;;MACA,oBADA,CAEA;;MACA;MACA;MACA;MACA,wEANA,CAOA;;MACA;QACA;QACA;QACA;MACA,CAJA;IAKA,CAfA;;IAgBA;IACAC,MAjBA,oBAiBA;MACA;MACA;IACA,CApBA;;IAqBA;IACAC,KAtBA,mBAsBA;MACA;QACAC,aADA;QAEAzD,uBAFA;QAGAC,uBAHA;QAIAyD,wBAJA;QAKAxD,oCALA;QAMAC,gCANA;QAOApB,eAPA;QAQAqB,uBARA;QASAuD,kBATA;QAUAtD,4BAVA;QAWAuD,yBAXA;QAYAtD,yBAZA;QAaAuD,qBAbA;QAcAtD,qBAdA;QAeAC,4BAfA;QAgBAsD,4BAhBA;QAiBAC,6BAjBA;QAkBAC,6BAlBA;QAmBAC,4BAnBA;QAoBAC,wBApBA;QAqBAC,gCArBA;QAsBAC,6BAtBA;QAuBAC,yBAvBA;QAwBA5D,oBAxBA;QAyBAE,iBAzBA;QA0BA2D,gCA1BA;QA2BAC,4BA3BA;QA4BAC,mCA5BA;QA6BAC,wBA7BA;QA8BAC,oBA9BA;QA+BAC,2BA/BA;QAgCAC,6BAhCA;QAiCAC,gCAjCA;QAkCAC,6BAlCA;QAmCAC,qCAnCA;QAoCAC,kCApCA;QAqCAC,mCArCA;QAsCAvE,4BAtCA;QAuCAwE;MAvCA;MAyCA;IACA,CAjEA;;IAkEA;IACAC,WAnEA,yBAmEA;MACA;MACA;IACA,CAtEA;;IAuEA;IACAC,UAxEA,wBAwEA;MACA;MACA;MACA;MACA;MACA;IACA,CA9EA;;IA+EA;IACAC,UAhFA,sBAgFAC,GAhFA,EAgFA;MAAA;;MACA;MACA;MACA;QACA;QACA;QACA;MACA,CAJA;IAKA,CAxFA;;IAyFA;IACAC,YA1FA,0BA0FA;MAAA;;MACA;MACA;MACAC;MACAA;MACA;MACA;MACA,wEAPA,CAQA;;MACA;QACA;QACA;MACA,CAHA,EAGAC,IAHA,CAGA;QACA;;QACA;MACA,CANA,EAMAC,KANA,CAMA,cANA;IAOA,CA1GA;;IA2GA;IACAC,mBA5GA,iCA4GA;MAAA;;MACA;MACA;MACA;QACA;UACA,uCADA,CAEA;;UACA;;UACA;QACA;MACA,CAPA,EAOAD,KAPA,CAOA;QACA;QACAE;MACA,CAVA;IAWA,CA1HA;;IA2HA;IACAC,UA5HA,wBA4HA;MAAA;;MACA,mCADA,CAEA;;MACA;MACA;QACA;;QACA;UACA,uCADA,CAEA;;UACA;;UACA;QACA,CALA,MAKA;UACA;UACA;UACA;QACA;MACA,CAZA,EAYAH,KAZA,CAYA;QACA;QACAE,mCAFA,CAGA;;QACA;QACA;MACA,CAlBA;IAmBA,CAnJA;;IAoJA;IACAE,gBArJA,8BAqJA;MAAA;;MACA;MACA;MACA;QACA;QACA;MACA,CAHA;IAIA,CA5JA;;IA6JA;IACAC,mBA9JA,+BA8JAC,MA9JA,EA8JA;MAAA;;MACA;QACA;;QACA;;QAEA;UACA;UACA;QACA,CAHA,MAGA;UACA;UACA;QACA;MACA,CAXA,EAWAN,KAXA,CAWA;QACA;QACA;;QACA;UACA;QACA;MACA,CAjBA;IAkBA,CAjLA;;IAkLA;IACAO,sBAnLA,oCAmLA;MAAA;;MACA;MACA;QACA;QACA,sCAFA,CAGA;;QACA;MACA,CALA,EAKAP,KALA,CAKA;QACA;MACA,CAPA;IAQA,CA7LA;;IA8LA;IACAQ,mBA/LA,iCA+LA;MACA;QACA;QACA;MACA;;MACA;IACA,CArMA;;IAsMA;IACAC,UAvMA,wBAuMA;MAAA;;MACA;QACA;QACA;MACA;;MAEA;QACA;;QACA;QAAA;QAAA;;QACA;UAAAC;UAAAC;QAAA;UACA;UACA;;UACA;;UACA;QACA,CALA,EAKAX,KALA,CAKA;UACA;QACA,CAPA;MAQA,CAXA;IAYA,CAzNA;;IA0NA;IACAY,YA3NA,0BA2NA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CAtOA;;IAuOA;IACAC,oBAxOA,gCAwOAP,MAxOA,EAwOA;MAAA;;MACA;MACAQ;;MAEA;QACA;UACA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;YACA;YACA;;YACA;;YACA,kBAJA,CAIA;;;YACAC;cACA;YACA,CAFA,EAEA,IAFA;UAGA,CARA,MAQA;YACA;;YACA;UACA,CAHA,MAGA;YACAA;UACA;QACA,CAzBA,EAyBAf,KAzBA,CAyBA;UACA;UACA;QACA,CA5BA;MA6BA,CA9BA;;MA+BAgB;IACA,CA5QA;IA6QAC,YA7QA,0BA6QA;MAAA;;MACA,0BADA,CAEA;;MACA;QACA;MACA;;MAEA;QACA;;QACA,0BAFA,CAGA;;QACA;QACA;QACA;QACA,8BAPA,CAQA;;QACA;MACA,CAVA,EAUAjB,KAVA,CAUA;QACA,iCADA,CAEA;;;QACA;;QACA;UACA;QACA;MACA,CAjBA,EAiBAkB,OAjBA,CAiBA;QACA,8BADA,CACA;MACA,CAnBA;IAoBA,CAxSA;;IAySA;IACAC,WA1SA,yBA0SA;MAAA;;MACA;MACA;QACA;QACA;QACA,qBACA;UAAAC;UAAAC;UAAAC;QAAA,CADA,EAEA;UAAAF;UAAAC;UAAAC;QAAA,CAFA,EAGA;UAAAF;UAAAC;UAAAC;QAAA,CAHA,EAIA;UAAAF;UAAAC;UAAAC;QAAA,CAJA,EAKA;UAAAF;UAAAC;UAAAC;QAAA,CALA,EAMA;UAAAF;UAAAC;UAAAC;QAAA,CANA,EAOA;UAAAF;UAAAC;UAAAC;QAAA,CAPA,EAQA;UAAAF;UAAAC;UAAAC;QAAA,CARA;MAUA,CAbA;IAcA,CA1TA;;IA2TA;IACAC,uBA5TA,mCA4TA3B,GA5TA,EA4TA;MAAA;;MACA;QACA;MACA,CAFA,EAEAG,IAFA,CAEA;QACA;;QACA;MACA,CALA,EAKAC,KALA,CAKA,cALA;IAMA,CAnUA;;IAoUA;IACAwB,oBArUA,gCAqUAC,IArUA,EAqUA;MACA;MACA;MACA,uCAHA,CAIA;;MACA;QACA;MACA;IACA,CA7UA;;IA8UA;IACAC,YA/UA,wBA+UApB,MA/UA,EA+UA;MACA;IACA,CAjVA;;IAkVA;IACAqB,aAnVA,yBAmVA1G,MAnVA,EAmVA;MACA;QACA;UAAA;;QACA;UAAA;;QACA;UAAA;;QACA;UAAA;;QACA;UAAA;MALA;IAOA,CA3VA;;IA4VA;IACA2G,aA7VA,yBA6VA3G,MA7VA,EA6VA;MACA;QACA;UAAA;;QACA;UAAA;;QACA;UAAA;;QACA;UAAA;;QACA;UAAA;MALA;IAOA,CArWA;;IAsWA;IACA4G,eAvWA,2BAuWAC,OAvWA,EAuWA;MACA,wBADA,CAEA;;MACA;IACA,CA3WA;;IA4WA;IACAC,cA7WA,0BA6WAN,IA7WA,EA6WA;MACA;MACA;QACAvF,qBADA;QAEAQ;MAFA,EAFA,CAOA;;MACA,oBARA,CAUA;;MACA;IACA,CAzXA;;IA0XA;IACAsF,mBA3XA,iCA2XA;MACA;MACA;IACA,CA9XA;;IA+XA;IACAC,qBAhYA,mCAgYA;MACA;MACA;IACA,CAnYA;;IAoYA;IACAC,wBArYA,sCAqYA;MAAA;;MACA;QACA;UACA;UACA;UACA;UACA;;UACA;QACA,CANA,MAMA;UACA;UACA;QACA;MACA,CAXA,EAWAlC,KAXA,CAWA;QACA;QACA;MACA,CAdA;IAeA,CArZA;;IAsZA;IACAmC,iBAvZA,+BAuZA;MAAA;;MACA;QACAC,uBADA;QAEAC,sBAFA;QAGAC;MAHA,GAIAvC,IAJA,CAIA;QACA;QACA;QACA;QACAwC;UACA;;UACA;QACA,CAHA,EAGAvC,KAHA,CAGA;UACA;QACA,CALA;MAMA,CAdA;IAeA,CAvaA;;IAwaA;IACAwC,yBAzaA,uCAyaA;MAAA;;MACA;QACAC;MACA;;MACA;QACA;MACA,CAFA,EAEA,IAFA;IAGA,CAhbA;;IAibA;IACAC,aAlbA,2BAkbA;MAAA;;MACA;MAEA;QACA;;QACA;UACAD;UACA;QACA;MACA,CANA;IAOA,CA5bA;;IA6bA;IACAE,iBA9bA,6BA8bAC,QA9bA,EA8bA;MACA;MACA;MACA;IACA,CAlcA;;IAmcA;IACAC,oBApcA,kCAocA;MAAA;;MACA;QACA;MACA,CAFA,EAEA7C,KAFA,CAEA,cAFA;IAGA,CAxcA;;IAycA;IACA8C,mCA1cA,iDA0cA;MAAA;;MACA;QACAV,uBADA;QAEAC,sBAFA;QAGAC;MAHA,GAIAvC,IAJA,CAIA;QACA;QACA;QACA;QAEA;UACA;;UACA;QACA,CAHA,EAGAC,KAHA,CAGA;UACA;QACA,CALA;MAMA,CAfA;IAgBA,CA3dA;;IA4dA;IACA+C,8BA7dA,4CA6dA;MAAA;;MACA;QACAN;MACA;;MACA;QACA;MACA,CAFA,EAEA,IAFA;IAGA,CApeA;;IAqeA;IACAO,uBAteA,qCAseA;MAAA;;MACA;MAEA;QACA;;QACA;UACAP;UACA,6BAFA,CAGA;;UACA;YACA;UACA;QACA;MACA,CAVA;IAWA;EApfA,CAlHA;EAwmBA;EACAQ,SAzmBA,uBAymBA;IACA;IACA;;IACA;MACA;IACA;EACA,CA/mBA;EAgnBAC,aAhnBA,2BAgnBA;IACA;MACAT;IACA;EACA;AApnBA,C", "names": ["RUNNING", "PAUSED", "COMPLETED", "FAILED", "name", "components", "Editor", "data", "loading", "exportLoading", "showSearch", "total", "list", "title", "open", "dateRangeTreatmentDatetime", "dateRangeLeaveHospitalDatetime", "dateRangeCreateTime", "queryParams", "pageNo", "pageSize", "hospitalCode", "hospitalName", "treatmentSerialNumberType", "treatmentSerialNumber", "idCardNumber", "mobilePhoneNumber", "departmentName", "<PERSON><PERSON><PERSON>", "mainDiagnosisCode", "orderType", "electronicBillIds", "status", "failType", "form", "rules", "syncDialogVisible", "syncForm", "date<PERSON><PERSON><PERSON>", "syncLoading", "syncProgress", "syncStatus", "progressText", "progressDialogVisible", "pickerOptions", "shortcuts", "text", "onClick", "picker", "start", "canResume", "resumeLoading", "syncCurrentPage", "syncTotalPages", "syncErrorMessage", "currentTaskId", "timeRange", "end", "statsDialogVisible", "statsData", "totalCount", "unfinishedTasksDialogVisible", "unfinishedTasksLoading", "unfinishedTasks", "lockedTaskIds", "SYNC_TASK_STATUS", "batchOpen", "batchTitle", "batchProgress", "batchTaskId", "progressTimer", "computed", "canDownloadReport", "created", "methods", "getList", "cancel", "reset", "id", "hospitalLevel", "address", "departmentCode", "<PERSON><PERSON><PERSON>", "mainDiagnosisName", "otherDiagnosisCode", "otherDiagnosisName", "treatmentDatetime", "mainComplaint", "currentMedicalHistory", "pastMedicalHistory", "geneticHistory", "hospitalizationNumber", "contactPersonName", "contactPersonPhoneNumber", "inpatientArea", "bedNumber", "complicationCode", "inHospitalDatetime", "leaveHospitalDatetime", "leaveHospitalState", "leaveHospitalDiagnosisName", "inHospitalDiagnosisName", "leaveHospitalDescription", "treatmentType", "handleQuery", "reset<PERSON><PERSON>y", "handleView", "row", "handleExport", "params", "then", "catch", "checkUnfinishedTask", "console", "handleSync", "syncLockStatuses", "checkTaskLockStatus", "taskId", "refreshUnfinishedTasks", "closeProgressDialog", "submitSync", "startTime", "endTime", "showProgress", "startProgressPolling", "localStorage", "setTimeout", "poll", "handleResume", "finally", "handleStats", "fieldName", "validCount", "percentage", "handleGenerateWorkOrder", "selectUnfinishedTask", "task", "isTaskLocked", "getStatusType", "getStatusText", "getShortMessage", "message", "viewTaskDetail", "handleBatchGenerate", "handleBatchRegenerate", "checkUnfinishedBatchTask", "startBatchProcess", "confirmButtonText", "cancelButtonText", "type", "processFunction", "startBatchProgressPolling", "clearInterval", "queryProgress", "getProgressStatus", "progress", "handleDownloadReport", "handleBatchSupplementElectronicBill", "startSupplementProgressPolling", "querySupplementProgress", "activated", "<PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": "src/views/insurance/wandaWorkOrder", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n\r\n    <!-- 搜索工具栏 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"100px\">\r\n      <el-form-item label=\"医院代码\" prop=\"hospitalCode\">\r\n        <el-input v-model=\"queryParams.hospitalCode\" placeholder=\"请输入医院代码\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"医院名称\" prop=\"hospitalName\">\r\n        <el-input v-model=\"queryParams.hospitalName\" placeholder=\"请输入医院名称\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"工单类型\" prop=\"treatmentSerialNumberType\">\r\n        <el-select v-model=\"queryParams.treatmentSerialNumberType\" placeholder=\"请选择工单类型\" clearable>\r\n          <el-option v-for=\"dict in this.getDictDatas(DICT_TYPE.INSURANCE_TREATMENT_TYPE)\"\r\n                     :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"就诊流水号\" prop=\"treatmentSerialNumber\">\r\n        <el-input v-model=\"queryParams.treatmentSerialNumber\" placeholder=\"请输入就诊流水号\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"姓名\" prop=\"name\">\r\n        <el-input v-model=\"queryParams.name\" placeholder=\"请输入姓名\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"身份证号码\" prop=\"idCardNumber\">\r\n        <el-input v-model=\"queryParams.idCardNumber\" placeholder=\"请输入身份证号码\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"移动电话\" prop=\"mobilePhoneNumber\">\r\n        <el-input v-model=\"queryParams.mobilePhoneNumber\" placeholder=\"请输入移动电话\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"科室名称\" prop=\"departmentName\">\r\n        <el-input v-model=\"queryParams.departmentName\" placeholder=\"请输入科室名称\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"医生姓名\" prop=\"doctorName\">\r\n        <el-input v-model=\"queryParams.doctorName\" placeholder=\"请输入医生姓名\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"主诊断编码\" prop=\"mainDiagnosisCode\">\r\n        <el-input v-model=\"queryParams.mainDiagnosisCode\" placeholder=\"请输入主诊断编码\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"就医时间\">\r\n        <el-date-picker v-model=\"dateRangeTreatmentDatetime\" style=\"width: 240px\" value-format=\"yyyy-MM-dd\"\r\n                        type=\"daterange\" range-separator=\"-\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"保险类型\" prop=\"orderType\">\r\n        <el-input v-model=\"queryParams.orderType\" placeholder=\"请输入保险类型\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"出院时间\">\r\n        <el-date-picker v-model=\"dateRangeLeaveHospitalDatetime\" style=\"width: 240px\" value-format=\"yyyy-MM-dd\"\r\n                        type=\"daterange\" range-separator=\"-\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker v-model=\"dateRangeCreateTime\" style=\"width: 240px\" value-format=\"yyyy-MM-dd\"\r\n                        type=\"daterange\" range-separator=\"-\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"工单状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择工单状态\" clearable>\r\n          <el-option label=\"未处理\" :value=\"0\" />\r\n          <el-option label=\"已生成工单\" :value=\"1\" />\r\n          <el-option label=\"生成失败\" :value=\"2\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"失败类型\" prop=\"failType\" v-if=\"queryParams.status === 2\">\r\n        <el-select v-model=\"queryParams.failType\" placeholder=\"请选择失败类型\" clearable>\r\n          <el-option v-for=\"dict in this.getDictDatas('wanda_order_fail_type')\"\r\n                     :key=\"dict.value\" :label=\"dict.label\" :value=\"parseInt(dict.value)\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作工具栏 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-refresh\" size=\"mini\" @click=\"handleSync\"\r\n                   v-hasPermi=\"['insurance:wanda-work-order:sync']\">同步预处理工单</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\" :loading=\"exportLoading\"\r\n                   v-hasPermi=\"['insurance:wanda-work-order:export']\">导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"info\" plain icon=\"el-icon-s-data\" size=\"mini\" @click=\"handleStats\"\r\n                   v-hasPermi=\"['insurance:wanda-work-order:stats']\">字段完整统计</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleBatchGenerate\"\r\n          v-hasPermi=\"['insurance:wanda-work-order:generate']\">\r\n          批量生成\r\n        </el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-refresh\" size=\"mini\" @click=\"handleBatchRegenerate\"\r\n          v-hasPermi=\"['insurance:wanda-work-order:generate']\">\r\n          批量重新生成\r\n        </el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-document-add\" size=\"mini\" @click=\"handleBatchSupplementElectronicBill\"\r\n          v-hasPermi=\"['insurance:wanda-work-order:update']\">\r\n          补充电子票据信息\r\n        </el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"list\" size=\"mini\" :header-cell-style=\"{background:'#F5F7FA'}\">\r\n      <el-table-column label=\"编号\" align=\"center\" prop=\"id\" width=\"80\" />\r\n      <el-table-column label=\"医院代码\" align=\"center\" prop=\"hospitalCode\" width=\"180\" show-overflow-tooltip />\r\n      <el-table-column label=\"医院代码\" align=\"center\" prop=\"hospitalName\" width=\"200\" show-overflow-tooltip />\r\n      <el-table-column label=\"工单类型\" align=\"center\" prop=\"treatmentSerialNumberType\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_TREATMENT_TYPE\" :value=\"scope.row.treatmentSerialNumberType\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"就诊流水号\" align=\"center\" prop=\"treatmentSerialNumber\" width=\"120\" show-overflow-tooltip />\r\n      <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"100\" />\r\n      <el-table-column label=\"身份证号码\" align=\"center\" prop=\"idCardNumber\" width=\"180\" show-overflow-tooltip />\r\n      <el-table-column label=\"移动电话\" align=\"center\" prop=\"mobilePhoneNumber\" width=\"120\" show-overflow-tooltip />\r\n      <el-table-column label=\"科室名称\" align=\"center\" prop=\"departmentName\" width=\"120\" show-overflow-tooltip />\r\n      <el-table-column label=\"医生姓名\" align=\"center\" prop=\"doctorName\" width=\"100\" />\r\n      <el-table-column label=\"主诊断编码\" align=\"center\" prop=\"mainDiagnosisCode\" width=\"120\" show-overflow-tooltip />\r\n      <el-table-column label=\"就医时间\" align=\"center\" prop=\"treatmentDatetime\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.treatmentDatetime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"保险类型\" align=\"center\" prop=\"orderType\" width=\"100\" />\r\n      <el-table-column label=\"出院时间\" align=\"center\" prop=\"leaveHospitalDatetime\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.leaveHospitalDatetime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"电子票据号\" align=\"center\" prop=\"electronicBillIds\" width=\"120\" show-overflow-tooltip />\r\n      <el-table-column label=\"就诊类型\" align=\"center\" prop=\"treatmentType\" width=\"100\" />\r\n      <el-table-column label=\"当前状态\" align=\"center\" prop=\"status\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.status === 0 ? '未处理' : scope.row.status === 1 ? '已生成工单' : '生成失败' }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column fixed=\"right\" label=\"操作\" align=\"center\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-view\" @click=\"handleView(scope.row)\"\r\n                     v-hasPermi=\"['insurance:wanda-work-order:query']\">查看详情</el-button>\r\n          <el-button v-if=\"scope.row.status !== 1\" size=\"mini\" type=\"text\" icon=\"el-icon-s-promotion\" @click=\"handleGenerateWorkOrder(scope.row)\"\r\n                     v-hasPermi=\"['insurance:wanda-work-order:generate']\">\r\n            {{scope.row.status === 2 ? '重新生成' : '生成工单'}}\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <!-- 分页组件 -->\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNo\" :limit.sync=\"queryParams.pageSize\"\r\n                @pagination=\"getList\"/>\r\n\r\n    <!-- 查看详情对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"医院信息\">\r\n          <div>医院代码：{{ form.hospitalCode }}</div>\r\n          <div>医院名称：{{ form.hospitalName }}</div>\r\n          <div>医院等级：{{ form.hospitalLevel }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"就诊信息\">\r\n          <div>工单类型：\r\n            <dict-tag :type=\"DICT_TYPE.INSURANCE_TREATMENT_TYPE\" :value=\"form.treatmentSerialNumberType\"/>\r\n          </div>\r\n          <div>就诊流水号：{{ form.treatmentSerialNumber }}</div>\r\n          <div>就诊时间：{{ parseTime(form.treatmentDatetime) }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"患者信息\">\r\n          <div>姓名：{{ form.name }}</div>\r\n          <div>身份证号：{{ form.idCardNumber }}</div>\r\n          <div>地址：{{ form.address }}</div>\r\n          <div>移动电话：{{ form.mobilePhoneNumber }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"就医科室\">\r\n          <div>科室编码：{{ form.departmentCode }}</div>\r\n          <div>科室名称：{{ form.departmentName }}</div>\r\n          <div>医生代码：{{ form.doctorCode }}</div>\r\n          <div>医生姓名：{{ form.doctorName }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"诊断信息\" :span=\"2\">\r\n          <div>主诊断编码：{{ form.mainDiagnosisCode }}</div>\r\n          <div>主诊断名称：{{ form.mainDiagnosisName }}</div>\r\n          <div>其他诊断编码：{{ form.otherDiagnosisCode }}</div>\r\n          <div>其他诊断名称：{{ form.otherDiagnosisName }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"病史信息\" :span=\"2\">\r\n          <div>主诉：{{ form.mainComplaint }}</div>\r\n          <div>现病史：{{ form.currentMedicalHistory }}</div>\r\n          <div>既往史：{{ form.pastMedicalHistory }}</div>\r\n          <div>遗传史：{{ form.geneticHistory }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"住院信息\" v-if=\"form.treatmentSerialNumberType === 1\">\r\n          <div>住院号：{{ form.hospitalizationNumber }}</div>\r\n          <div>病区：{{ form.inpatientArea }}</div>\r\n          <div>床号：{{ form.bedNumber }}</div>\r\n          <div>并发症代码：{{ form.complicationCode }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"住院时间信息\" v-if=\"form.treatmentSerialNumberType === 1\">\r\n          <div>住院时间：{{ parseTime(form.inHospitalDatetime) }}</div>\r\n          <div>出院时间：{{ parseTime(form.leaveHospitalDatetime) }}</div>\r\n          <div>出院状态：{{ form.leaveHospitalState }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"诊断记录\" :span=\"2\" v-if=\"form.treatmentSerialNumberType === 1\">\r\n          <div>入院诊断：{{ form.inHospitalDiagnosisName }}</div>\r\n          <div>出院诊断：{{ form.leaveHospitalDiagnosisName }}</div>\r\n          <div>出院描述：{{ form.leaveHospitalDescription }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"其他信息\" :span=\"2\">\r\n          <div>电子票据号：{{ form.electronicBillIds }}</div>\r\n          <div>就诊类型：{{ form.treatmentType }}</div>\r\n          <div>保险类型：{{ form.orderType }}</div>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item v-if=\"form.status === 2\" label=\"失败信息\" :span=\"2\">\r\n          <div>\r\n            <span style=\"font-weight: bold;\">失败类型：</span>\r\n            <dict-tag type=\"wanda_order_fail_type\" :value=\"form.failType\"/>\r\n          </div>\r\n          <div style=\"color: #F56C6C; margin-top: 5px;\">\r\n            <span style=\"font-weight: bold;\">失败原因：</span>{{ form.failReason }}\r\n          </div>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"open = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加同步对话框 -->\r\n    <el-dialog title=\"同步预处理工单\" :visible.sync=\"syncDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"syncForm\" :model=\"syncForm\" label-width=\"100px\">\r\n        <el-form-item label=\"时间范围\" required>\r\n          <el-date-picker\r\n            v-model=\"syncForm.dateRange\"\r\n            type=\"datetimerange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始时间\"\r\n            end-placeholder=\"结束时间\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            :picker-options=\"pickerOptions\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"syncDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitSync\" :loading=\"syncLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加进度对话框 -->\r\n    <el-dialog title=\"同步进度\" :visible.sync=\"progressDialogVisible\" width=\"400px\" \r\n      :close-on-click-modal=\"false\" @closed=\"closeProgressDialog\">\r\n      <div class=\"progress-content\">\r\n        <el-progress :percentage=\"syncProgress\" :status=\"syncStatus\"></el-progress>\r\n        <div class=\"progress-text\">{{progressText}}</div>\r\n        <div v-if=\"syncErrorMessage\" class=\"error-message\">{{syncErrorMessage}}</div>\r\n        <div class=\"progress-info\" v-if=\"syncCurrentPage && syncTotalPages\">\r\n          当前进度: {{syncCurrentPage}}/{{syncTotalPages}}页\r\n        </div>\r\n        <div class=\"time-range-info\" v-if=\"timeRange.start && timeRange.end\">\r\n          同步时间: {{timeRange.start}} 至 {{timeRange.end}}\r\n        </div>\r\n        <el-button \r\n          v-if=\"canResume\" \r\n          type=\"primary\" \r\n          @click=\"handleResume\"\r\n          :loading=\"resumeLoading\"\r\n          style=\"margin-top: 15px\">\r\n          继续同步\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 字段完整统计对话框 -->\r\n    <el-dialog title=\"字段完整统计\" :visible.sync=\"statsDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-table :data=\"statsData\" border style=\"width: 100%\">\r\n        <el-table-column prop=\"fieldName\" label=\"字段名称\" width=\"180\"/>\r\n        <el-table-column prop=\"validCount\" label=\"完整数据数量\"/>\r\n        <el-table-column prop=\"percentage\" label=\"完整度\">\r\n          <template slot-scope=\"scope\">\r\n            <el-progress :percentage=\"scope.row.percentage\"></el-progress>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div style=\"margin-top: 20px\">\r\n        <strong>数据总数：{{ totalCount }}</strong>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"statsDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 未完成任务列表对话框 -->\r\n    <el-dialog title=\"未完成的同步任务\" :visible.sync=\"unfinishedTasksDialogVisible\" width=\"800px\" append-to-body>\r\n      <el-table v-loading=\"unfinishedTasksLoading\" :data=\"unfinishedTasks\" border style=\"width: 100%\" \r\n               :header-cell-style=\"{background:'#F5F7FA'}\">\r\n        <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"180\" align=\"center\"/>\r\n        <el-table-column prop=\"endTime\" label=\"结束时间\" width=\"180\" align=\"center\"/>\r\n        <el-table-column label=\"同步进度\" width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-progress :percentage=\"scope.row.progress\" \r\n                          :status=\"scope.row.status === SYNC_TASK_STATUS.RUNNING ? '' : 'exception'\">\r\n            </el-progress>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"状态信息\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tooltip placement=\"top\" :disabled=\"!scope.row.errorMessage\">\r\n              <div slot=\"content\" v-if=\"scope.row.errorMessage\">\r\n                <div style=\"max-width: 300px; word-break: break-all;\">{{ scope.row.errorMessage }}</div>\r\n              </div>\r\n              <div>\r\n                <el-tag :type=\"getStatusType(scope.row.status)\" size=\"mini\">\r\n                  {{ getStatusText(scope.row.status) }}\r\n                </el-tag>\r\n                <div class=\"status-message\">\r\n                  {{ getShortMessage(scope.row.message) }}\r\n                </div>\r\n              </div>\r\n            </el-tooltip>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"180\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div class=\"action-column\">\r\n              <el-button \r\n                type=\"primary\" \r\n                size=\"mini\" \r\n                v-if=\"!isTaskLocked(scope.row.taskId)\"\r\n                @click=\"selectUnfinishedTask(scope.row)\">\r\n                继续同步\r\n              </el-button>\r\n              <el-button \r\n                v-if=\"isTaskLocked(scope.row.taskId)\"\r\n                type=\"info\" \r\n                size=\"mini\" \r\n                @click=\"viewTaskDetail(scope.row)\"\r\n                style=\"margin-top: 5px\">\r\n                查看详情\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"unfinishedTasksDialogVisible = false\">关 闭</el-button>\r\n        <el-button type=\"primary\" @click=\"refreshUnfinishedTasks\">刷 新</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量生成进度对话框 -->\r\n    <el-dialog :title=\"batchTitle\" :visible.sync=\"batchOpen\" width=\"500px\" append-to-body>\r\n      <div v-if=\"batchProgress\">\r\n        <el-progress :percentage=\"batchProgress.progress\" :status=\"getProgressStatus(batchProgress)\"></el-progress>\r\n        <div class=\"progress-info\">\r\n          <p>总数: {{ batchProgress.totalCount }}</p>\r\n          <p>已处理: {{ batchProgress.processedCount }}</p>\r\n          <p>成功: {{ batchProgress.successCount }}</p>\r\n          <p>失败: {{ batchProgress.failCount }}</p>\r\n          <p>状态: {{ batchProgress.message }}</p>\r\n          <p v-if=\"batchProgress.errorMessage\" class=\"error-message\">错误: {{ batchProgress.errorMessage }}</p>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"batchOpen = false\">关 闭</el-button>\r\n        <el-button type=\"primary\" @click=\"handleDownloadReport\" v-if=\"canDownloadReport\">下载报告</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  createWandaWorkOrder,\r\n  updateWandaWorkOrder,\r\n  deleteWandaWorkOrder,\r\n  getWandaWorkOrder,\r\n  getWandaWorkOrderPage,\r\n  exportWandaWorkOrderExcel,\r\n  startSync,\r\n  getSyncProgress,\r\n  resumeSync,\r\n  generateWorkOrder,\r\n  getUnfinishedTaskList,\r\n  checkTaskLock,\r\n  batchGenerateWorkOrder,\r\n  getBatchGenerateProgress,\r\n  downloadBatchGenerateReport,\r\n  batchRegenerateFailedWorkOrder,\r\n  getUnfinishedBatchTasks,\r\n  batchSupplementElectronicBill,\r\n  getBatchSupplementProgress\r\n} from \"@/api/insurance/wandaWorkOrder\";\r\nimport Editor from '@/components/Editor';\r\nimport { getWandaWorkOrderStats } from \"@/api/insurance/wandaWorkOrder\";\r\n\r\n// 添加同步任务状态常量\r\nconst SYNC_TASK_STATUS = {\r\n  RUNNING: 'RUNNING',\r\n  PAUSED: 'PAUSED',\r\n  COMPLETED: 'COMPLETED',\r\n  FAILED: 'FAILED'\r\n};\r\n\r\nexport default {\r\n  name: \"WandaWorkOrder\",\r\n  components: {\r\n    Editor,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 导出遮罩层\r\n      exportLoading: false,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 预处理工单列表\r\n      list: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      dateRangeTreatmentDatetime: [],\r\n      dateRangeLeaveHospitalDatetime: [],\r\n      dateRangeCreateTime: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNo: 1,\r\n        pageSize: 10,\r\n        hospitalCode: null,\r\n        hospitalName: null,\r\n        treatmentSerialNumberType: null,\r\n        treatmentSerialNumber: null,\r\n        name: null,\r\n        idCardNumber: null,\r\n        mobilePhoneNumber: null,\r\n        departmentName: null,\r\n        doctorName: null,\r\n        mainDiagnosisCode: null,\r\n        orderType: null,\r\n        electronicBillIds: null,\r\n        status: null,\r\n        failType: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      },\r\n      // 同步对话框\r\n      syncDialogVisible: false,\r\n      syncForm: {\r\n        dateRange: [],\r\n      },\r\n      syncLoading: false,\r\n      syncProgress: 0,\r\n      syncStatus: '',\r\n      progressText: '',\r\n      progressDialogVisible: false,\r\n      pickerOptions: {\r\n        shortcuts: [{\r\n          text: '最近一周',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }, {\r\n          text: '最近一个月',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }]\r\n      },\r\n      canResume: false,\r\n      resumeLoading: false,\r\n      syncCurrentPage: 0,\r\n      syncTotalPages: 0,\r\n      syncErrorMessage: '',\r\n      currentTaskId: null,\r\n      timeRange: {\r\n        start: '',\r\n        end: ''\r\n      },\r\n      statsDialogVisible: false,\r\n      statsData: [],\r\n      totalCount: 0,\r\n      unfinishedTasksDialogVisible: false,\r\n      unfinishedTasksLoading: false,\r\n      unfinishedTasks: [],\r\n      lockedTaskIds: [],\r\n      // 添加SYNC_TASK_STATUS到data中，这样模板可以访问它\r\n      SYNC_TASK_STATUS: SYNC_TASK_STATUS,\r\n      batchOpen: false,\r\n      batchTitle: '',\r\n      batchProgress: null,\r\n      batchTaskId: null,\r\n      progressTimer: null,\r\n    };\r\n  },\r\n  computed: {\r\n    canDownloadReport() {\r\n      return this.batchProgress && \r\n             (this.batchProgress.status === 'COMPLETED' || this.batchProgress.status === 'FAILED') && \r\n             this.batchProgress.processedCount > 0;\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.checkUnfinishedTask();\r\n  },\r\n  methods: {\r\n    /** 查询列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 处理查询参数\r\n      let params = {...this.queryParams};\r\n      this.addBeginAndEndTime(params, this.dateRangeTreatmentDatetime, 'treatmentDatetime');\r\n      this.addBeginAndEndTime(params, this.dateRangeLeaveHospitalDatetime, 'leaveHospitalDatetime');\r\n      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime');\r\n      // 执行查询\r\n      getWandaWorkOrderPage(params).then(response => {\r\n        this.list = response.data.list;\r\n        this.total = response.data.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        id: undefined,\r\n        hospitalCode: undefined,\r\n        hospitalName: undefined,\r\n        hospitalLevel: undefined,\r\n        treatmentSerialNumberType: undefined,\r\n        treatmentSerialNumber: undefined,\r\n        name: undefined,\r\n        idCardNumber: undefined,\r\n        address: undefined,\r\n        mobilePhoneNumber: undefined,\r\n        departmentCode: undefined,\r\n        departmentName: undefined,\r\n        doctorCode: undefined,\r\n        doctorName: undefined,\r\n        mainDiagnosisCode: undefined,\r\n        mainDiagnosisName: undefined,\r\n        otherDiagnosisCode: undefined,\r\n        otherDiagnosisName: undefined,\r\n        treatmentDatetime: undefined,\r\n        mainComplaint: undefined,\r\n        currentMedicalHistory: undefined,\r\n        pastMedicalHistory: undefined,\r\n        geneticHistory: undefined,\r\n        orderType: undefined,\r\n        status: undefined,\r\n        hospitalizationNumber: undefined,\r\n        contactPersonName: undefined,\r\n        contactPersonPhoneNumber: undefined,\r\n        inpatientArea: undefined,\r\n        bedNumber: undefined,\r\n        complicationCode: undefined,\r\n        inHospitalDatetime: undefined,\r\n        leaveHospitalDatetime: undefined,\r\n        leaveHospitalState: undefined,\r\n        leaveHospitalDiagnosisName: undefined,\r\n        inHospitalDiagnosisName: undefined,\r\n        leaveHospitalDescription: undefined,\r\n        electronicBillIds: undefined,\r\n        treatmentType: undefined,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNo = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRangeTreatmentDatetime = [];\r\n      this.dateRangeLeaveHospitalDatetime = [];\r\n      this.dateRangeCreateTime = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 查看按钮操作 */\r\n    handleView(row) {\r\n      this.reset();\r\n      const id = row.id;\r\n      getWandaWorkOrder(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"查看预处理工单\";\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 处理查询参数\r\n      let params = {...this.queryParams};\r\n      params.pageNo = undefined;\r\n      params.pageSize = undefined;\r\n      this.addBeginAndEndTime(params, this.dateRangeTreatmentDatetime, 'treatmentDatetime');\r\n      this.addBeginAndEndTime(params, this.dateRangeLeaveHospitalDatetime, 'leaveHospitalDatetime');\r\n      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime');\r\n      // 执行导出\r\n      this.$modal.confirm('是否确认导出所有预处理工单数据项?').then(() => {\r\n          this.exportLoading = true;\r\n          return exportWandaWorkOrderExcel(params);\r\n        }).then(response => {\r\n          this.$download.excel(response, '预处理工单.xls');\r\n          this.exportLoading = false;\r\n        }).catch(() => {});\r\n    },\r\n    /** 打开未完成任务列表对话框时，重置锁定状态 */\r\n    checkUnfinishedTask() {\r\n      // 清空锁定任务状态\r\n      this.lockedTaskIds = [];\r\n      getUnfinishedTaskList().then(response => {\r\n        if (response.data && response.data.length > 0) {\r\n          this.unfinishedTasks = response.data;\r\n          // 检查后端锁定状态\r\n          this.syncLockStatuses();\r\n          this.unfinishedTasksDialogVisible = true;\r\n        }\r\n      }).catch(error => {\r\n        this.unfinishedTasksLoading = false;\r\n        console.error(\"检查未完成任务异常:\", error);\r\n      });\r\n    },\r\n    /** 同步按钮操作 */\r\n    handleSync() {\r\n      this.unfinishedTasksLoading = true;\r\n      // 清空锁定任务状态\r\n      this.lockedTaskIds = [];\r\n      getUnfinishedTaskList().then(response => {\r\n        this.unfinishedTasksLoading = false;\r\n        if (response.data && response.data.length > 0) {\r\n          this.unfinishedTasks = response.data;\r\n          // 检查后端锁定状态\r\n          this.syncLockStatuses();\r\n          this.unfinishedTasksDialogVisible = true;\r\n        } else {\r\n          // 没有未完成的任务,打开同步对话框\r\n          this.syncDialogVisible = true;\r\n          this.syncForm.dateRange = [];\r\n        }\r\n      }).catch(error => {\r\n        this.unfinishedTasksLoading = false;\r\n        console.error(\"获取未完成任务异常:\", error);\r\n        // 出错时也打开同步对话框\r\n        this.syncDialogVisible = true;\r\n        this.syncForm.dateRange = [];\r\n      });\r\n    },\r\n    /** 检查后端锁定状态 */\r\n    syncLockStatuses() {\r\n      // 创建新的API接口来获取所有任务的锁定状态\r\n      // 由于现在没有批量检查锁状态的API，这里默认所有任务没有锁定\r\n      this.unfinishedTasks.forEach(task => {\r\n        // 为每个任务创建单独的锁检查请求\r\n        this.checkTaskLockStatus(task.taskId);\r\n      });\r\n    },\r\n    /** 检查单个任务锁状态 */\r\n    checkTaskLockStatus(taskId) {\r\n      checkTaskLock(taskId).then(response => {\r\n        const isLocked = response.data === true;\r\n        const index = this.lockedTaskIds.indexOf(taskId);\r\n        \r\n        if (isLocked && index === -1) {\r\n          // 添加到锁定列表\r\n          this.lockedTaskIds.push(taskId);\r\n        } else if (!isLocked && index > -1) {\r\n          // 从锁定列表中移除\r\n          this.lockedTaskIds.splice(index, 1);\r\n        }\r\n      }).catch(() => {\r\n        // 如果检查失败，假定任务未锁定\r\n        const index = this.lockedTaskIds.indexOf(taskId);\r\n        if (index > -1) {\r\n          this.lockedTaskIds.splice(index, 1);\r\n        }\r\n      });\r\n    },\r\n    /** 刷新未完成任务列表 */\r\n    refreshUnfinishedTasks() {\r\n      this.unfinishedTasksLoading = true;\r\n      getUnfinishedTaskList().then(response => {\r\n        this.unfinishedTasks = response.data || [];\r\n        this.unfinishedTasksLoading = false;\r\n        // 刷新锁状态\r\n        this.syncLockStatuses();\r\n      }).catch(() => {\r\n        this.unfinishedTasksLoading = false;\r\n      });\r\n    },\r\n    /** 关闭进度对话框时清理锁状态 */\r\n    closeProgressDialog() {\r\n      if (this.currentTaskId) {\r\n        // 当用户手动关闭进度对话框时，检查后端锁状态\r\n        this.checkTaskLockStatus(this.currentTaskId);\r\n      }\r\n      this.progressDialogVisible = false;\r\n    },\r\n    /** 提交同步 */\r\n    submitSync() {\r\n      if (!this.syncForm.dateRange || this.syncForm.dateRange.length !== 2) {\r\n        this.$message.error('请选择时间范围');\r\n        return;\r\n      }\r\n      \r\n      this.$modal.confirm('是否确认同步该时间范围内的预处理工单?').then(() => {\r\n        this.syncLoading = true;\r\n        const [startTime, endTime] = this.syncForm.dateRange;\r\n        startSync({ startTime, endTime }).then(response => {\r\n          this.syncDialogVisible = false;\r\n          this.syncLoading = false;\r\n          this.showProgress();\r\n          this.startProgressPolling(response.data);\r\n        }).catch(() => {\r\n          this.syncLoading = false;\r\n        });\r\n      });\r\n    },\r\n    /** 显示进度对话框 */\r\n    showProgress() {\r\n      this.progressDialogVisible = true;\r\n      this.syncProgress = 0;\r\n      this.syncStatus = '';\r\n      this.progressText = '正在同步...';\r\n      this.syncErrorMessage = '';\r\n      this.canResume = false;\r\n      this.syncCurrentPage = 0;\r\n      this.syncTotalPages = 0;\r\n      this.timeRange.start = '';\r\n      this.timeRange.end = '';\r\n    },\r\n    /** 开始轮询进度 */\r\n    startProgressPolling(taskId) {\r\n      this.currentTaskId = taskId;\r\n      localStorage.setItem('currentSyncTaskId', taskId);\r\n      \r\n      const poll = () => {\r\n        getSyncProgress(taskId).then(response => {\r\n          const { progress, status, message, canResume, currentPage, totalPages, errorMessage, startTime, endTime } = response.data;\r\n          this.syncProgress = progress;\r\n          this.progressText = message;\r\n          this.canResume = canResume;\r\n          this.syncCurrentPage = currentPage;\r\n          this.syncTotalPages = totalPages;\r\n          this.syncErrorMessage = errorMessage;\r\n          this.timeRange.start = startTime;\r\n          this.timeRange.end = endTime;\r\n          \r\n          if (status === 'COMPLETED') {\r\n            this.syncStatus = 'success';\r\n            this.progressText = '同步完成';\r\n            this.$message.success('同步完成');\r\n            this.getList(); // 刷新列表\r\n            setTimeout(() => {\r\n              this.progressDialogVisible = false;\r\n            }, 1500);\r\n          } else if (status === 'FAILED' || status === 'PAUSED') {\r\n            this.syncStatus = 'exception';\r\n            this.$message.error(errorMessage || '同步失败');\r\n          } else {\r\n            setTimeout(poll, 1000);\r\n          }\r\n        }).catch(() => {\r\n          this.syncStatus = 'exception';\r\n          this.progressText = '获取进度失败';\r\n        });\r\n      };\r\n      poll();\r\n    },\r\n    handleResume() {\r\n      this.resumeLoading = true;\r\n      // 标记任务为锁定状态\r\n      if (!this.lockedTaskIds.includes(this.currentTaskId)) {\r\n        this.lockedTaskIds.push(this.currentTaskId);\r\n      }\r\n      \r\n      resumeSync(this.currentTaskId).then(() => {\r\n        this.$message.success('已开始继续同步');\r\n        this.canResume = false;\r\n        // 重置进度状态\r\n        this.syncStatus = '';\r\n        this.syncProgress = 0;\r\n        this.progressText = '正在同步...';\r\n        this.syncErrorMessage = '';\r\n        // 重新开始轮询进度\r\n        this.startProgressPolling(this.currentTaskId);\r\n      }).catch(() => {\r\n        this.$message.error('继续同步失败');\r\n        // 失败时移除锁定标记\r\n        const index = this.lockedTaskIds.indexOf(this.currentTaskId);\r\n        if (index > -1) {\r\n          this.lockedTaskIds.splice(index, 1);\r\n        }\r\n      }).finally(() => {\r\n        this.resumeLoading = false;  // 无论成功失败都要关闭loading状态\r\n      });\r\n    },\r\n    /** 统计按钮操作 */\r\n    handleStats() {\r\n      this.statsDialogVisible = true;\r\n      getWandaWorkOrderStats().then(response => {\r\n        const data = response.data;\r\n        this.totalCount = data.totalCount;\r\n        this.statsData = [\r\n          { fieldName: '手机号', validCount: data.mobilePhoneNumberCount, percentage: Math.round(data.mobilePhoneNumberCount / data.totalCount * 100) },\r\n          { fieldName: '主诉', validCount: data.mainComplaintCount, percentage: Math.round(data.mainComplaintCount / data.totalCount * 100) },\r\n          { fieldName: '现病史', validCount: data.currentMedicalHistoryCount, percentage: Math.round(data.currentMedicalHistoryCount / data.totalCount * 100) },\r\n          { fieldName: '既往史', validCount: data.pastMedicalHistoryCount, percentage: Math.round(data.pastMedicalHistoryCount / data.totalCount * 100) },\r\n          { fieldName: '遗传史', validCount: data.geneticHistoryCount, percentage: Math.round(data.geneticHistoryCount / data.totalCount * 100) },\r\n          { fieldName: '主诊断编码', validCount: data.mainDiagnosisCodeCount, percentage: Math.round(data.mainDiagnosisCodeCount / data.totalCount * 100) },\r\n          { fieldName: '主诊断名称', validCount: data.mainDiagnosisNameCount, percentage: Math.round(data.mainDiagnosisNameCount / data.totalCount * 100) },\r\n          { fieldName: '电子票据号', validCount: data.electronicBillIdsCount, percentage: Math.round(data.electronicBillIdsCount / data.totalCount * 100) }\r\n        ];\r\n      });\r\n    },\r\n    /** 生成工单按钮操作 */\r\n    handleGenerateWorkOrder(row) {\r\n      this.$modal.confirm('是否确认生成工单?').then(() => {\r\n        return generateWorkOrder(row.id);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(\"生成成功\");\r\n        this.getList();\r\n      }).catch(() => {});\r\n    },\r\n    /** 选择未完成任务继续同步 */\r\n    selectUnfinishedTask(task) {\r\n      this.unfinishedTasksDialogVisible = false;\r\n      this.showProgress();\r\n      this.startProgressPolling(task.taskId);\r\n      // 标记任务为锁定状态\r\n      if (!this.lockedTaskIds.includes(task.taskId)) {\r\n        this.lockedTaskIds.push(task.taskId);\r\n      }\r\n    },\r\n    /** 判断任务是否被锁定 */\r\n    isTaskLocked(taskId) {\r\n      return this.lockedTaskIds.includes(taskId);\r\n    },\r\n    /** 获取状态标签类型 */\r\n    getStatusType(status) {\r\n      switch (status) {\r\n        case SYNC_TASK_STATUS.RUNNING: return 'success';\r\n        case SYNC_TASK_STATUS.PAUSED: return 'warning';\r\n        case SYNC_TASK_STATUS.FAILED: return 'danger';\r\n        case SYNC_TASK_STATUS.COMPLETED: return 'info';\r\n        default: return 'info';\r\n      }\r\n    },\r\n    /** 获取状态文本 */\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case SYNC_TASK_STATUS.RUNNING: return '同步中';\r\n        case SYNC_TASK_STATUS.PAUSED: return '已暂停';\r\n        case SYNC_TASK_STATUS.FAILED: return '同步失败';\r\n        case SYNC_TASK_STATUS.COMPLETED: return '已完成';\r\n        default: return '未知状态';\r\n      }\r\n    },\r\n    /** 获取简短消息 */\r\n    getShortMessage(message) {\r\n      if (!message) return '';\r\n      // 截取前30个字符，如果有更多则添加省略号\r\n      return message.length > 30 ? message.substring(0, 30) + '...' : message;\r\n    },\r\n    /** 查看任务详情 */\r\n    viewTaskDetail(task) {\r\n      this.currentTaskId = task.taskId;\r\n      this.timeRange = {\r\n        start: task.startTime,\r\n        end: task.endTime\r\n      };\r\n      \r\n      // 显示进度对话框\r\n      this.showProgress();\r\n      \r\n      // 开始轮询更新进度信息\r\n      this.startProgressPolling(task.taskId);\r\n    },\r\n    /** 批量生成按钮操作 */\r\n    handleBatchGenerate() {\r\n      this.batchTitle = '批量生成工单';\r\n      this.checkUnfinishedBatchTask();\r\n    },\r\n    /** 批量重新生成按钮操作 */\r\n    handleBatchRegenerate() {\r\n      this.batchTitle = '批量重新生成失败工单';\r\n      this.checkUnfinishedBatchTask();\r\n    },\r\n    /** 检查未完成的批量任务 */\r\n    checkUnfinishedBatchTask() {\r\n      getUnfinishedBatchTasks().then(response => {\r\n        if (response.data && response.data.length > 0) {\r\n          // 有未完成的任务,显示进度对话框\r\n          this.batchOpen = true;\r\n          this.batchProgress = response.data[0];\r\n          this.batchTaskId = response.data[0].taskId;\r\n          this.startBatchProgressPolling();\r\n        } else {\r\n          // 没有未完成的任务,开始新的批量处理\r\n          this.startBatchProcess();\r\n        }\r\n      }).catch(() => {\r\n        // 出错时也开始新的批量处理\r\n        this.startBatchProcess();\r\n      });\r\n    },\r\n    /** 开始批量处理 */\r\n    startBatchProcess() {\r\n      this.$confirm('确认要执行此操作吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.batchOpen = true;\r\n        this.batchProgress = null;\r\n        const processFunction = this.batchTitle === '批量生成工单' ? batchGenerateWorkOrder : batchRegenerateFailedWorkOrder;\r\n        processFunction(this.queryParams).then(response => {\r\n          this.batchTaskId = response.data;\r\n          this.startBatchProgressPolling();\r\n        }).catch(() => {\r\n          this.batchOpen = false;\r\n        });\r\n      });\r\n    },\r\n    /** 开始轮询进度 */\r\n    startBatchProgressPolling() {\r\n      if (this.progressTimer) {\r\n        clearInterval(this.progressTimer);\r\n      }\r\n      this.progressTimer = setInterval(() => {\r\n        this.queryProgress();\r\n      }, 1000);\r\n    },\r\n    /** 查询进度 */\r\n    queryProgress() {\r\n      if (!this.batchTaskId) return;\r\n      \r\n      getBatchGenerateProgress(this.batchTaskId).then(response => {\r\n        this.batchProgress = response.data;\r\n        if (this.batchProgress.status === 'COMPLETED' || this.batchProgress.status === 'FAILED') {\r\n          clearInterval(this.progressTimer);\r\n          this.progressTimer = null;\r\n        }\r\n      });\r\n    },\r\n    /** 获取进度条状态 */\r\n    getProgressStatus(progress) {\r\n      if (progress.status === 'FAILED') return 'exception';\r\n      if (progress.status === 'COMPLETED') return 'success';\r\n      return '';\r\n    },\r\n    /** 下载报告 */\r\n    handleDownloadReport() {\r\n      downloadBatchGenerateReport(this.batchTaskId).then(response => {\r\n        this.$download.excel(response, '批量生成工单报告.xlsx');\r\n      }).catch(() => {});\r\n    },\r\n    /** 补充电子票据信息按钮操作 */\r\n    handleBatchSupplementElectronicBill() {\r\n      this.$confirm('此操作将补充状态为待接单且票据完整度为无数据或不确定的工单的电子票据信息，确认要执行吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.batchTitle = '补充电子票据信息';\r\n        this.batchOpen = true;\r\n        this.batchProgress = null;\r\n\r\n        batchSupplementElectronicBill().then(response => {\r\n          this.batchTaskId = response.data;\r\n          this.startSupplementProgressPolling();\r\n        }).catch(() => {\r\n          this.batchOpen = false;\r\n        });\r\n      });\r\n    },\r\n    /** 开始轮询补充电子票据信息进度 */\r\n    startSupplementProgressPolling() {\r\n      if (this.progressTimer) {\r\n        clearInterval(this.progressTimer);\r\n      }\r\n      this.progressTimer = setInterval(() => {\r\n        this.querySupplementProgress();\r\n      }, 1000);\r\n    },\r\n    /** 查询补充电子票据信息进度 */\r\n    querySupplementProgress() {\r\n      if (!this.batchTaskId) return;\r\n\r\n      getBatchSupplementProgress(this.batchTaskId).then(response => {\r\n        this.batchProgress = response.data;\r\n        if (this.batchProgress.status === 'COMPLETED' || this.batchProgress.status === 'FAILED') {\r\n          clearInterval(this.progressTimer);\r\n          this.progressTimer = null;\r\n          // 任务完成后刷新列表\r\n          if (this.batchProgress.status === 'COMPLETED') {\r\n            this.getList();\r\n          }\r\n        }\r\n      });\r\n    },\r\n  },\r\n  // 添加页面激活时的处理\r\n  activated() {\r\n    // 从localStorage获取任务ID\r\n    const taskId = localStorage.getItem('currentSyncTaskId');\r\n    if (taskId) {\r\n      this.checkUnfinishedTask();\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    if (this.progressTimer) {\r\n      clearInterval(this.progressTimer);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.progress-content {\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n.progress-text {\r\n  margin-top: 10px;\r\n  color: #606266;\r\n}\r\n.error-message {\r\n  color: #F56C6C;\r\n  margin-top: 10px;\r\n  font-size: 14px;\r\n}\r\n.progress-info {\r\n  margin-top: 10px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n.time-range-info {\r\n  margin-top: 10px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n.el-descriptions {\r\n  margin: 20px;\r\n}\r\n.el-descriptions-item__content {\r\n  line-height: 24px;\r\n}\r\n.el-descriptions-item__content div {\r\n  margin: 8px 0;\r\n}\r\n.status-message {\r\n  margin-top: 5px;\r\n  font-size: 12px;\r\n  color: #606266;\r\n}\r\n.progress-text {\r\n  display: block;\r\n  font-size: 12px;\r\n  margin-top: 5px;\r\n}\r\n.status-message {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-top: 5px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n.action-column {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.action-column .el-button {\r\n  margin-left: 0; /* 覆盖element-ui的默认margin */\r\n  width: 90px;    /* 固定宽度确保按钮统一 */\r\n}\r\n</style>\r\n"]}]}