{"remainingRequest": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\company\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\company\\index.vue", "mtime": 1754462373388}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\babel.config.js", "mtime": 1691889630125}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1667694382043}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1667694382624}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;AAiGA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAA,eADA;EAEAC;IACAC;EADA,CAFA;EAKAC,IALA,kBAKA;IACA;MACA;MACAC,aAFA;MAGA;MACAC,oBAJA;MAKA;MACAC,gBANA;MAOA;MACAC,QARA;MASA;MACAC,QAVA;MAWA;MACAC,SAZA;MAaA;MACAC,WAdA;MAeA;MACAC;QACAC,SADA;QAEAC,YAFA;QAGAC,UAHA;QAIAd,UAJA;QAKAe,eALA;QAMAC;MANA,CAhBA;MAwBA;MACAC,QAzBA;MA0BA;MACAC;QACAJ;UAAAK;UAAAC;UAAAC;QAAA,EADA;QAEArB;UAAAmB;UAAAC;UAAAC;QAAA;MAFA;IA3BA;EAgCA,CAtCA;EAuCAC,OAvCA,qBAuCA;IACA;EACA,CAzCA;EA0CAC;IACA;IACAC,OAFA,qBAEA;MAAA;;MACA,oBADA,CAEA;;MACA,+DAHA,CAIA;;MACA;QACA;QACA;QACA;MACA,CAJA;IAKA,CAZA;;IAaA;IACAC,MAdA,oBAcA;MACA;MACA;IACA,CAjBA;;IAkBA;IACAC,KAnBA,mBAmBA;MACA;QACAC,aADA;QAEAb,eAFA;QAGAd,eAHA;QAIA4B,kBAJA;QAKAb,oBALA;QAMAC,gBANA;QAOAa,mBAPA;QAQAC,yBARA;QASAC,wBATA;QAUAC;MAVA;MAYA;IACA,CAjCA;;IAkCA;IACAC,WAnCA,yBAmCA;MACA;MACA;IACA,CAtCA;;IAuCA;IACAC,UAxCA,wBAwCA;MACA;MACA;IACA,CA3CA;;IA4CA;IACAC,SA7CA,uBA6CA;MACA;MACA;MACA;IACA,CAjDA;;IAkDA;IACAC,YAnDA,wBAmDAC,GAnDA,EAmDA;MAAA;;MACA;MACA;MACA;QACA;QACA;QACA;MACA,CAJA;IAKA,CA3DA;;IA4DA;IACAC,UA7DA,wBA6DA;MAAA;;MACA;QACA;UACA;QACA,CAHA,CAIA;;;QACA;UACA;YACA;;YACA;;YACA;UACA,CAJA;UAKA;QACA,CAZA,CAaA;;;QACA;UACA;;UACA;;UACA;QACA,CAJA;MAKA,CAnBA;IAoBA,CAlFA;;IAmFA;IACAC,YApFA,wBAoFAF,GApFA,EAoFA;MAAA;;MACA;MACA;QACA;MACA,CAFA,EAEAG,IAFA,CAEA;QACA;;QACA;MACA,CALA,EAKAC,KALA,CAKA,cALA;IAMA,CA5FA;;IA6FA;IACAC,YA9FA,0BA8FA;MAAA;;MACA;MACA;MACAC;MACAA,4BAJA,CAKA;;MACA;QACA;QACA;MACA,CAHA,EAGAH,IAHA,CAGA;QACA;;QACA;MACA,CANA,EAMAC,KANA,CAMA,cANA;IAOA;EA3GA;AA1CA,C", "names": ["name", "components", "UserSelect", "data", "loading", "exportLoading", "showSearch", "total", "list", "title", "open", "queryParams", "pageNo", "pageSize", "code", "contacter", "phone", "form", "rules", "required", "message", "trigger", "created", "methods", "getList", "cancel", "reset", "id", "address", "accounts", "oauth2ClientId", "<PERSON><PERSON><PERSON>", "encryptionIv", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "handleUpdate", "row", "submitForm", "handleDelete", "then", "catch", "handleExport", "params"], "sourceRoot": "src/views/insurance/company", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n\r\n    <!-- 搜索工作栏 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"公司代码\" prop=\"code\">\r\n        <el-input v-model=\"queryParams.code\" placeholder=\"请输入公司代码\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\" prop=\"name\">\r\n        <el-input v-model=\"queryParams.name\" placeholder=\"请输入公司名称\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系人\" prop=\"contacter\">\r\n        <el-input v-model=\"queryParams.contacter\" placeholder=\"请输入联系人\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系方式\" prop=\"phone\">\r\n        <el-input v-model=\"queryParams.phone\" placeholder=\"请输入联系方式\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作工具栏 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n                   v-hasPermi=\"['insurance:company:create']\">新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\" :loading=\"exportLoading\"\r\n                   v-hasPermi=\"['insurance:company:export']\">导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"list\">\r\n      <el-table-column label=\"编号\" align=\"center\" type=\"index\" />\r\n      <el-table-column label=\"公司代码\" align=\"center\" prop=\"code\" />\r\n      <el-table-column label=\"公司名称\" align=\"center\" prop=\"name\" />\r\n      <el-table-column label=\"公司地址\" align=\"center\" prop=\"address\" />\r\n      <el-table-column label=\"联系人\" align=\"center\" prop=\"contacter\" />\r\n      <el-table-column label=\"联系方式\" align=\"center\" prop=\"phone\" />\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\"\r\n                     v-hasPermi=\"['insurance:company:update']\">修改</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\"\r\n                     v-hasPermi=\"['insurance:company:delete']\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <!-- 分页组件 -->\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNo\" :limit.sync=\"queryParams.pageSize\"\r\n                @pagination=\"getList\"/>\r\n\r\n    <!-- 对话框(添加 / 修改) -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-form-item label=\"公司代码\" prop=\"code\">\r\n          <el-input v-model=\"form.code\" placeholder=\"请输入公司代码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"公司名称\" prop=\"name\">\r\n          <el-input v-model=\"form.name\" placeholder=\"请输入公司名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"公司地址\" prop=\"address\">\r\n          <el-input v-model=\"form.address\" placeholder=\"请输入公司地址\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"联系人\" prop=\"contacter\">\r\n          <el-input v-model=\"form.contacter\" placeholder=\"请输入联系人\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"联系方式\" prop=\"phone\">\r\n          <el-input v-model=\"form.phone\" placeholder=\"请输入联系方式\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"关联用户\" prop=\"accounts\" >\r\n          <user-select v-model=\"form.accounts\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"oauth2客户端id\" prop=\"oauth2ClientId\" >\r\n          <el-input v-model=\"form.oauth2ClientId\" placeholder=\"请输入关联oauth2客户端id\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"加密Key\" prop=\"encryptionKey\">\r\n          <el-input v-model=\"form.encryptionKey\" placeholder=\"请输入加密Key\" show-password />\r\n        </el-form-item>\r\n        <el-form-item label=\"加密IV\" prop=\"encryptionIv\">\r\n          <el-input v-model=\"form.encryptionIv\" placeholder=\"请输入加密IV\" show-password />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { createCompany, updateCompany, deleteCompany, getCompany, getCompanyPage, exportCompanyExcel } from \"@/api/insurance/company\";\r\nimport UserSelect from \"@/components/UserSelect/UserSelect\";\r\nexport default {\r\n  name: \"Company\",\r\n  components: {\r\n    UserSelect\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 导出遮罩层\r\n      exportLoading: false,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 保险公司列表\r\n      list: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNo: 1,\r\n        pageSize: 10,\r\n        code: null,\r\n        name: null,\r\n        contacter: null,\r\n        phone: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        code: [{ required: true, message: \"公司代码不能为空\", trigger: \"blur\" }],\r\n        name: [{ required: true, message: \"公司名称不能为空\", trigger: \"blur\" }],\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 处理查询参数\r\n      let params = {...this.queryParams};\r\n      // 执行查询\r\n      getCompanyPage(params).then(response => {\r\n        this.list = response.data.list;\r\n        this.total = response.data.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        id: undefined,\r\n        code: undefined,\r\n        name: undefined,\r\n        address: undefined,\r\n        contacter: undefined,\r\n        phone: undefined,\r\n        accounts: undefined,\r\n        oauth2ClientId: undefined,\r\n        encryptionKey: undefined,\r\n        encryptionIv: undefined\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNo = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加保险公司\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id;\r\n      getCompany(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改保险公司\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (!valid) {\r\n          return;\r\n        }\r\n        // 修改的提交\r\n        if (this.form.id != null) {\r\n          updateCompany(this.form).then(response => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.getList();\r\n          });\r\n          return;\r\n        }\r\n        // 添加的提交\r\n        createCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"新增成功\");\r\n          this.open = false;\r\n          this.getList();\r\n        });\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const id = row.id;\r\n      this.$modal.confirm('是否确认删除保险公司编号为\"' + id + '\"的数据项?').then(function() {\r\n          return deleteCompany(id);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 处理查询参数\r\n      let params = {...this.queryParams};\r\n      params.pageNo = undefined;\r\n      params.pageSize = undefined;\r\n      // 执行导出\r\n      this.$modal.confirm('是否确认导出所有保险公司数据项?').then(() => {\r\n          this.exportLoading = true;\r\n          return exportCompanyExcel(params);\r\n        }).then(response => {\r\n          this.$download.excel(response, '保险公司.xls');\r\n          this.exportLoading = false;\r\n        }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}