
f1f92386268f7da6526b04197e19a5b686581827	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"73093c98b541eb1a84a38a53b1fcf921\"}","integrity":"sha512-izKf//U3I5n1TM5935aPACLvL3YkTpRwx5EEtXNzq6LshaVlSkSmuwtL3Dil74QIlb8LKGfdrwcyNOYipaFD8w==","time":1754634590571,"size":16769767}