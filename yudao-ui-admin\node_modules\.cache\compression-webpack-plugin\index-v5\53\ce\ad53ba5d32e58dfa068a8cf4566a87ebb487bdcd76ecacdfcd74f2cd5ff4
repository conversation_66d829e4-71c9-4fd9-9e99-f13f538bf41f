
e41694722ac4d32205c01c80c43c969dc3ea0f69	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fchunk-vendors.js\",\"contentHash\":\"f9313bdf3ce5efb64d814dd1976984aa\"}","integrity":"sha512-sedn9rtV9OfwcYB8uuTGkC+aHZz9HKWGnX1tsnScoxHPeKDwcYDertOvvJcOQe12pM+AJQlR1fgG5+pba2PKBQ==","time":1754634589734,"size":12241171}