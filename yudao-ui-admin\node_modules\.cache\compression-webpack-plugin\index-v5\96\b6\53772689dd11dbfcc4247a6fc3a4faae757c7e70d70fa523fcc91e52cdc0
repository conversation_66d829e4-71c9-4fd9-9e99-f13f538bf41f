
b8c264c3b9308076e86442cde5f6f7863b3bd7d6	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"0ea1ca6d28d9237caf7664999b3cefca\"}","integrity":"sha512-tb3+uXjBqLIhZSbvIQzlYw4z40el1xgj/rmX0eMTogZgqLIYlzQ3j5kZCICzKsps2s6kSLPAPpGxXaLaBKVDxA==","time":1754634588854,"size":3474871}