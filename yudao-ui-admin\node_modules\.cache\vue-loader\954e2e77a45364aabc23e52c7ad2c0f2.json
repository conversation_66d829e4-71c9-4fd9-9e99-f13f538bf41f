{"remainingRequest": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\company\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\company\\index.vue", "mtime": 1754462373388}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1667694382043}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1667694382624}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/insurance/company", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n\r\n    <!-- 搜索工作栏 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"公司代码\" prop=\"code\">\r\n        <el-input v-model=\"queryParams.code\" placeholder=\"请输入公司代码\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\" prop=\"name\">\r\n        <el-input v-model=\"queryParams.name\" placeholder=\"请输入公司名称\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系人\" prop=\"contacter\">\r\n        <el-input v-model=\"queryParams.contacter\" placeholder=\"请输入联系人\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系方式\" prop=\"phone\">\r\n        <el-input v-model=\"queryParams.phone\" placeholder=\"请输入联系方式\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作工具栏 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n                   v-hasPermi=\"['insurance:company:create']\">新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\" :loading=\"exportLoading\"\r\n                   v-hasPermi=\"['insurance:company:export']\">导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"list\">\r\n      <el-table-column label=\"编号\" align=\"center\" type=\"index\" />\r\n      <el-table-column label=\"公司代码\" align=\"center\" prop=\"code\" />\r\n      <el-table-column label=\"公司名称\" align=\"center\" prop=\"name\" />\r\n      <el-table-column label=\"公司地址\" align=\"center\" prop=\"address\" />\r\n      <el-table-column label=\"联系人\" align=\"center\" prop=\"contacter\" />\r\n      <el-table-column label=\"联系方式\" align=\"center\" prop=\"phone\" />\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\"\r\n                     v-hasPermi=\"['insurance:company:update']\">修改</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\"\r\n                     v-hasPermi=\"['insurance:company:delete']\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <!-- 分页组件 -->\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNo\" :limit.sync=\"queryParams.pageSize\"\r\n                @pagination=\"getList\"/>\r\n\r\n    <!-- 对话框(添加 / 修改) -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-form-item label=\"公司代码\" prop=\"code\">\r\n          <el-input v-model=\"form.code\" placeholder=\"请输入公司代码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"公司名称\" prop=\"name\">\r\n          <el-input v-model=\"form.name\" placeholder=\"请输入公司名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"公司地址\" prop=\"address\">\r\n          <el-input v-model=\"form.address\" placeholder=\"请输入公司地址\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"联系人\" prop=\"contacter\">\r\n          <el-input v-model=\"form.contacter\" placeholder=\"请输入联系人\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"联系方式\" prop=\"phone\">\r\n          <el-input v-model=\"form.phone\" placeholder=\"请输入联系方式\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"关联用户\" prop=\"accounts\" >\r\n          <user-select v-model=\"form.accounts\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"oauth2客户端id\" prop=\"oauth2ClientId\" >\r\n          <el-input v-model=\"form.oauth2ClientId\" placeholder=\"请输入关联oauth2客户端id\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"加密Key\" prop=\"encryptionKey\">\r\n          <el-input v-model=\"form.encryptionKey\" placeholder=\"请输入加密Key\" show-password />\r\n        </el-form-item>\r\n        <el-form-item label=\"加密IV\" prop=\"encryptionIv\">\r\n          <el-input v-model=\"form.encryptionIv\" placeholder=\"请输入加密IV\" show-password />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { createCompany, updateCompany, deleteCompany, getCompany, getCompanyPage, exportCompanyExcel } from \"@/api/insurance/company\";\r\nimport UserSelect from \"@/components/UserSelect/UserSelect\";\r\nexport default {\r\n  name: \"Company\",\r\n  components: {\r\n    UserSelect\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 导出遮罩层\r\n      exportLoading: false,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 保险公司列表\r\n      list: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNo: 1,\r\n        pageSize: 10,\r\n        code: null,\r\n        name: null,\r\n        contacter: null,\r\n        phone: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        code: [{ required: true, message: \"公司代码不能为空\", trigger: \"blur\" }],\r\n        name: [{ required: true, message: \"公司名称不能为空\", trigger: \"blur\" }],\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 处理查询参数\r\n      let params = {...this.queryParams};\r\n      // 执行查询\r\n      getCompanyPage(params).then(response => {\r\n        this.list = response.data.list;\r\n        this.total = response.data.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        id: undefined,\r\n        code: undefined,\r\n        name: undefined,\r\n        address: undefined,\r\n        contacter: undefined,\r\n        phone: undefined,\r\n        accounts: undefined,\r\n        oauth2ClientId: undefined,\r\n        encryptionKey: undefined,\r\n        encryptionIv: undefined\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNo = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加保险公司\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id;\r\n      getCompany(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改保险公司\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (!valid) {\r\n          return;\r\n        }\r\n        // 修改的提交\r\n        if (this.form.id != null) {\r\n          updateCompany(this.form).then(response => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.getList();\r\n          });\r\n          return;\r\n        }\r\n        // 添加的提交\r\n        createCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"新增成功\");\r\n          this.open = false;\r\n          this.getList();\r\n        });\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const id = row.id;\r\n      this.$modal.confirm('是否确认删除保险公司编号为\"' + id + '\"的数据项?').then(function() {\r\n          return deleteCompany(id);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 处理查询参数\r\n      let params = {...this.queryParams};\r\n      params.pageNo = undefined;\r\n      params.pageSize = undefined;\r\n      // 执行导出\r\n      this.$modal.confirm('是否确认导出所有保险公司数据项?').then(() => {\r\n          this.exportLoading = true;\r\n          return exportCompanyExcel(params);\r\n        }).then(response => {\r\n          this.$download.excel(response, '保险公司.xls');\r\n          this.exportLoading = false;\r\n        }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}